import os
from pathlib import Path
from src.utils.theme_setting import theme_setting
from PySide6.QtCore import Qt, <PERSON><PERSON><PERSON><PERSON>,QTimer,QPoint,QSize, QUrl
import math
from PySide6.QtGui import QGuiApplication, QStandardItem, QIcon, QResizeEvent
from PySide6.QtWidgets import Q<PERSON>ridLayout, QWidget, QStackedWidget, QVBoxLayout, QLabel,QSizePolicy, QFrame,QHBoxLayout
from src.common.widget.button_state import ButtonState
from src.styles.style import Style
from src.common.widget.camera_widget import CameraWidget, StreamCameraType
from src.common.controller.main_controller import main_controller
from src.common.model.main_tree_view_model import TreeType
from src.utils.camera_qsettings import Camera_Qsettings, DeleteType,UpdateType
from src.common.widget.widget_for_custom_grid.map_grid_item_widget import MapGridWidgetItem
from src.common.model.device_models import TabType
from src.common.key_board.key_board_manager import key_board_manager
from src.common.camera.grid_item_selected import grid_item_selected
from src.common.model.tab_model import TabModel,tab_model_manager,GridItem,ItemType
from src.common.model.camera_model import Camera,CameraModel,CameraModelManager
from src.common.model.item_grid_model import ItemGridModel
from src.presentation.camera_screen.stream_object_base_widget import StreamObjectBaseWidget
from typing import List
from src.common.widget.event.event_widget import EventWidget
import threading
import resources_rc
from src.common.model.event_data_model import EventAI
from src.common.model.camera_model import camera_model_manager
from src.common.camera.video_capture import video_capture_controller
from PySide6.QtQuickWidgets import QQuickWidget
from src.presentation.camera_screen.managers.grid_manager import GridModel, gridManager
import logging
logger = logging.getLogger(__name__)
class VirtualCameraGridWidget(QWidget):
    def __init__(self, parent=None, width=960, height=480,
                 screen_index=0, item: QStandardItem = None,gridModel = None):
        super().__init__(parent)
        print("Initializing VirtualCameraGridWidget with shared GridModel...")
        # Use shared GridModel instead of creating new one
        self.gridModel = gridModel
        self.setWindowIcon(QIcon(Style.PrimaryImage.icon128))
        print(f"Using shared GridModel with ID: {self.gridModel.get_property('id', 'unknown')}")
        self.item = item
        if self.item is None:
            logger.debug(f'gggggggggggg')
        if self.item is not None:
            self.item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'open_all_virtual')))
        self.grid_width = width
        self.grid_height = height
        self.resize_timer = None

        self.setMinimumWidth(self.grid_width)
        self.setMinimumHeight(self.grid_height)
        self.screen_index = screen_index
        self.frame = QFrame(self)
        self.main_widget = QWidget(self.frame)
        self.main_widget.setObjectName("main_widget")
        self.main_widget.setStyleSheet(f'''
            QWidget#main_widget {{
                background-color: {main_controller.get_theme_attribute('Color', 'main_background')};
            }}
        ''')
        
        self.main_screen = QGuiApplication.screens()[self.screen_index]
        # Đăng ký nhận tín hiệu thay đổi độ phân giải từ OS
        self.main_screen.geometryChanged.connect(self.on_screen_geometry_changed)
        screen_geometry = self.main_screen.geometry()

        self.grid_width = screen_geometry.width()
        self.grid_height = screen_geometry.height()
        self.frame.setGeometry(QRect(0, 0, self.grid_width, self.grid_height))
        self.current_grid_value = None
        self.list_camera_stream_widget = {}
        self.camera_grid_layout = QVBoxLayout()
        self.camera_grid_layout.setContentsMargins(0, 0, 0, 0)
        self.camera_grid_layout.setSpacing(0)
        self.camera_grid_layout.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft)

        self.quick_widget = QQuickWidget()
        self.quick_widget.setResizeMode(QQuickWidget.ResizeMode.SizeRootObjectToView)
        self.quick_widget.rootContext().setContextProperty("gridModel", self.gridModel)
        self.quick_widget.setSource(QUrl("qrc:src/presentation/camera_screen/components/CameraGrid.qml"))
        # self.quick_widget.setResizeMode(QQuickWidget.ResizeMode.SizeRootObjectToView)
        # self.quick_widget = QQuickWidget()
        # self.quick_widget.setResizeMode(QQuickWidget.ResizeMode.SizeRootObjectToView)
        
        # # Get the absolute path to the QML file
        # current_dir = Path(__file__).parent
        # qml_file = current_dir / "components" / "CameraGrid.qml"
        # qml_path = os.path.abspath(qml_file)
        
        # # Add QML import path
        # engine = self.quick_widget.engine()

        # context = engine.rootContext()
        # print("Registering GridModel with QML context...")
        # context.setContextProperty("gridModel", self.gridModel)
        # print("GridModel registered with QML context")
        
        # import_path = os.path.dirname(qml_path)
        # print(f"QML import path: {import_path}")
        # engine.addImportPath(import_path)
        
        # # Load QML
        # print(f"Loading QML from: {qml_path}")
        # self.quick_widget.setSource(QUrl.fromLocalFile(qml_path))
        
        # Check for QML loading errors
        if self.quick_widget.status() == QQuickWidget.Error:
            errors = self.quick_widget.errors()
            print("QML Loading Errors:")
            for error in errors:
                print(f"  - {error}")
        else:
            print("QML loaded successfully")
        self.camera_grid_layout.addWidget(self.quick_widget)

        self.main_widget.setLayout(self.camera_grid_layout)
        self.setGeometry(screen_geometry)
        self.tab_closed = False
        self.setWindowTitle(self.gridModel.get_property("name","ahihi"))
        main_controller.list_parent[self.gridModel.get_property("id",None)] = [screen_index,self]
        logger.info(f'VirtualCameraGridWidget done')

    def resizeEvent(self, event):
        super().resizeEvent(event)
        logger.info(f'resizeEvent = {self.width(),self.height()}')
        self.quick_widget.setGeometry(0, 0, self.width(), self.height())
        
    # def id_changed(self, id):
    #     main_controller.list_parent[id] = [self.screen_index,self]

    def on_screen_geometry_changed(self, geometry):
        self.grid_width = geometry.width()
        self.grid_height = geometry.height()
        self.frame.setGeometry(QRect(0, 0, self.grid_width, self.grid_height))
        self.setGeometry(geometry)
        new_size = QSize(self.grid_width, self.grid_height)
        self.main_widget.resize(new_size)
        self.change_grid_view(self.tab_model.data.currentGrid)
        self.setGeometry(geometry)
        self.raise_()

    def closeEvent(self, event):
        main_tree_view = main_controller.list_parent['MainTreeView']
        item  = main_tree_view.get_item(name=self.gridModel.get_property("name",None),tree_type = TreeType.Virtual_Window_Item,server_ip = self.gridModel.get_property("server_ip",None))
        if item is not None:
            item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'close_all_virtual')))
        if self.gridModel.get_property("id",None) in main_controller.list_parent:
            del main_controller.list_parent[self.gridModel.get_property("id",None)]
        new_custom_tab_widget = main_controller.list_parent['CustomTabWidget']
        # trường hợp người dùng đóng cưỡng bức cửa sổ Virtual thì cần tìm tab Virtual để đóng nó lại
        if not self.tab_closed:
            for index in range(new_custom_tab_widget.tab_widget.count()):
                widget = new_custom_tab_widget.getWidgetByIndex(index)
                tab_name = new_custom_tab_widget.tab_widget.tab_bar.tabText(
                    index)
                if widget.gridModel.get_property("id",None) == self.gridModel.get_property("id",None):
                    new_custom_tab_widget.tab_widget.removeTab(index)
                    break

    def switch_window(self,screen_index):
        self.screen_index = screen_index
        screen_geometry = QGuiApplication.screens()[self.screen_index].geometry()
        pre_width = self.grid_width
        pre_height = self.grid_height
        self.grid_width = screen_geometry.width()
        self.grid_height = screen_geometry.height()
        self.frame.setGeometry(QRect(0, 0, self.grid_width, self.grid_height))
        # Khi người dùng switch thì tại thời điểm đó cần ưu tiên hiển thị Virtual lên trên các ứng dụng khác trên màn hình
        # kiểm tra nếu màn hình có độ phân giải khác thì xử lý việc resize lại
        if pre_width != self.grid_width or pre_height != self.grid_height:
            new_size = QSize(self.grid_width, self.grid_height)
            self.main_widget.resize(new_size)
            # self.change_grid_view(self.tab_model.data.currentGrid)
        self.setGeometry(screen_geometry)
        self.raise_()

    # # kich ban Shortcut ID
    # def create_screen_index_widget(self):
    #     widget = QWidget(self)
    #     layout = QHBoxLayout()
    #     # margin left, right 8px
    #     layout.setContentsMargins(8, 0, 8, 0)
    #     layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

    #     self.screen_index = QLabel()
    #     self.screen_index.setStyleSheet(Style.StyleSheet.label_style2)
    #     self.screen_index.setAlignment(Qt.AlignmentFlag.AlignCenter)
    #     layout.addWidget(self.screen_index)
    #     widget.setLayout(layout)
    #     return widget
    
    # def show_screen_index(self,number):
    #     self.screen_index.setVisible(True)
    #     self.screen_index.setText(str(number))
    #     timer = QTimer(self)
    #     timer.setInterval(5000)  
    #     timer.setSingleShot(True)
    #     timer.timeout.connect(self.hide_screen_index)
    #     timer.start()
    #     key_board_manager.timer_list.append(timer)

    # def change_screen_index_color(self):
    #     self.screen_index.setStyleSheet(Style.StyleSheet.label_style4)

    # def hide_screen_index(self):
    #     self.screen_index.setStyleSheet(Style.StyleSheet.label_style2)
    #     self.screen_index.setVisible(False)
    #     if str(Qt.Key.Key_Alt) in key_board_manager.keys:
    #         key_board_manager.keys = {}

    # def show_item_index(self):
    #     for index in range(self.grid.count()):
    #         item = self.grid.itemAt(index).widget()
    #         if index in self.list_camera_stream_widget:
    #             camera_stream = self.list_camera_stream_widget[index]
    #             camera_stream.show_grid_item_index(index)
    #         else:
    #             item.show_grid_item_index(index)

    # def find_item_index(self, number):
    #     logger.info(f'VirtualCameraGridWidget find_item_index = {number}')
    #     # sau khi đã sử dụng phím tắt đê lựa chọn được number của grid rồi thì cần focus vào item đó
    #     for index in range(self.grid.count()):
    #         if index == number:
    #             grid_item = self.tab_model.data.listGridData.get(index,None)
    #             if grid_item is not None:
    #                 grid_item.virtual_widget.stack_item.grid_item_clicked(main_controller.current_tab)
    #                 grid_item.virtual_widget.stack_item.change_grid_item_index_color()
                    
    # def process_change_item(self,action = 4):
    #     # action = 4 <=> bam phim Qt.Key.Key_Left 
    #     # action = 6 <=> bam phim Qt.Key.Key_Right
    #     key_selected = 0
    #     widget = grid_item_selected.data['widget']
    #     if widget is not None:
    #         # for key in range(self.grid.count()):
    #         #     stack_item = self.grid.itemAt(key).widget()
    #         for key, grid_item in self.tab_model.data.listGridData.items():
    #             if grid_item.virtual_widget == widget:
    #                 logger.debug(f'process_change_item = {key}')

    #                 if action == 4:
    #                     key_selected = key - 1 if key > 0 else 0
    #                 elif action == 6:
    #                     key_selected = key + 1 if key < self.grid.count() - 1 else key
    #                 break
    #         grid_item = self.tab_model.data.listGridData.get(key_selected,None)
    #         # logger.debug(f'key_selected = {grid_item.widget} : {key_selected}')

    #         if grid_item is not None:
    #             if grid_item.virtual_widget is not None:
    #                 grid_item.virtual_widget.stack_item.grid_item_clicked(main_controller.current_tab)
  
    # def get_index_of_item_from_row_col(self, target_row, target_col):
    #     for index in range(self.grid.count()):
    #         item = self.grid.itemAt(index)
    #         row, col, row_span, col_span = self.grid.getItemPosition(index)

    #         # Check if the target position is within the span of the item
    #         if (
    #                 row <= target_row < row + row_span
    #                 and col <= target_col < col + col_span
    #         ):
    #             return index
    #     # If no item is found within the span, calculate the index
    #     index_calculated = int(target_row * self.grid.columnCount() + target_col)
    #     return index_calculated
    
    # def remove_grid_item_signal(self,index):
    #     grid_item = self.tab_model.data.listGridData.get(index,None)
    #     if grid_item is not None:
    #         virtual_widget = grid_item.virtual_widget
    #         is_selected = True if grid_item_selected.data['widget'] == virtual_widget else False
    #         stack_item: StackFrame = virtual_widget.stack_item
    #         if grid_item.is_virtual_fullscreen:
    #             # logger.info(f"Removed grid item1")

    #             # virtual_widget = self.fullscreen_grid_item(index)
    #             if virtual_widget is not None:
    #                 # stack_item = virtual_widget.stack_item
    #                 if isinstance(virtual_widget, CameraWidget):
    #                     self.stop_live_handle(virtual_widget)
    #                     # widget.stop_video()
    #                     virtual_widget.deleteLater()
    #                     stack_item.removeWidget(virtual_widget)
    #                 self.root_stackedwidget.removeWidget(stack_item)
    #                 self.root_stackedwidget.setCurrentIndex(0)
    #                 # grid_item.is_virtual_fullscreen = False
    #                 stack_item = virtual_widget.stack_item
    #                 background_widget = stack_item.add_widget()
    #                 self.tab_model.data.listGridData[stack_item.index].type = ItemType.Label
    #                 self.tab_model.data.listGridData[stack_item.index].model = 'Label'
    #                 self.tab_model.data.listGridData[stack_item.index].virtual_widget = background_widget
    #                 return
    #             # sau khi fullscreen -> cần truyền lại giá trị stack item và widget mới để remove khỏi grid
    #         if isinstance(virtual_widget, CameraWidget):
    #             self.stop_live_handle(virtual_widget)
    #             virtual_widget.deleteLater()
    #             stack_item.removeWidget(virtual_widget)
    #         elif isinstance(virtual_widget, Map2DWidgetItem):
    #             virtual_widget.deleteLater()
    #             stack_item.removeWidget(virtual_widget)
    #         elif isinstance(virtual_widget, EventWidget):
    #             virtual_widget.deleteLater()
    #             stack_item.removeWidget(virtual_widget)
    #         background_widget = stack_item.add_widget()
    #         self.tab_model.data.listGridData[stack_item.index].type = ItemType.Label
    #         self.tab_model.data.listGridData[stack_item.index].model = 'Label'
    #         self.tab_model.data.listGridData[stack_item.index].virtual_widget = background_widget
    #         if is_selected:
    #             stack_item.grid_item_clicked(main_controller.current_tab)
    #         # if self.tab_type != TabType.Invalid:
    #         #     self.tab_model.to_qsetting()

    # def change_grid_view_signal(self,data):
    #     self.change_grid_view(data)

    # def change_grid_view(self, data):
    #     # print(f'change_grid_view1 = {data}')
    #     grid_model = ItemGridModel.from_dict(data)
    #     if self.camera_grid_layout.count() == 1 and self.root_stackedwidget is not None:
    #         self.root_stackedwidget.close()
    #         self.camera_grid_layout.removeWidget(self.root_stackedwidget)
    #     # create new grid view
    #     self.root_stackedwidget = QStackedWidget()
    #     self.root_stackedwidget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
    #     self.root_stackedwidget.setContentsMargins(0, 0, 0, 0)

    #     self.ui_gridview(0, grid_model)
    #     self.camera_grid_layout.addWidget(self.root_stackedwidget)
    #     # self.camera_grid_layout.setAlignment(self.root_stackedwidget, Qt.AlignmentFlag.AlignCenter)
    #     # sau
    #     # if self.temp_grid_layout >= 2 then remove all except last item
    #     # self.diff_list_camera_stream()
    #     self.root_stackedwidget.widget(0).show()

    # def ui_gridview(self, page_index, grid_model):
    #     # divisions = grid_model.divisions
    #     row_from_model = grid_model.row
    #     col_from_model = grid_model.column
    #     grid_count = grid_model.total_grid_count
    #     data_merge_list = grid_model.data
    #     grid_type = grid_model.grid_type
    #     divisions_type = grid_model.divisions_type
        
    #     widget_contain_grid = QWidget()
    #     widget_contain_grid.resizeEvent = lambda event: self.on_resize_event(widget_contain_grid, event)
    #     self.root_stackedwidget.addWidget(widget_contain_grid)
    #     self.root_stackedwidget.widget(0).hide()
    #     margin = 5
    #     max_height = self.grid_height - margin
    #     max_width = self.grid_width - margin
    #     item_aspect_ratio = 16 / 9
    #     self.grid = QGridLayout()
    #     self.grid.setAlignment(Qt.AlignmentFlag.AlignTop | Qt.AlignmentFlag.AlignLeft)
    #     self.grid.setContentsMargins(0, 0, 0, 0)
    #     self.grid.setSpacing(3)

    #     if divisions_type == ButtonState.DivisionType.CUSTOM_DIVISIONS:
    #         size = int(math.sqrt(grid_count))
    #         self.span_and_create_custom_divisions(size=size, excluded_positions=data_merge_list, rows=row_from_model,
    #                                               cols=col_from_model)
    #     else:
    #         width_calculate = int(max_width / math.sqrt(grid_count))
    #         height_calculate = int(width_calculate / item_aspect_ratio)
    #         if height_calculate * math.sqrt(grid_count) > self.grid_height:
    #             self.frame_height = int(self.grid_height / math.sqrt(grid_count))
    #             self.frame_width = int((self.frame_height) * item_aspect_ratio)
    #         else:
    #             self.frame_width = width_calculate
    #             self.frame_height = height_calculate
    #         for row in range(row_from_model):
    #             for col in range(col_from_model):
    #                 root_camera_widget = StackFrame(parent=self, frame_width=self.frame_width,
    #                                                 frame_height=self.frame_height, position=QPoint(row, col))
    #                 root_camera_widget.resizeEvent = lambda event: self.item_resize_event(root_camera_widget, event)
    #                 root_camera_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)
    #                 root_camera_widget.screen_name = self.tab_model.data.id
    #                 self.add_grid_item(stack_item = root_camera_widget,row=row,col=col,width=self.frame_width,height=self.frame_height)

    #                 self.grid.addWidget(root_camera_widget, row, col)
    #     self.root_stackedwidget.setFixedSize(self.grid_width, self.grid_height)
    #     self.root_stackedwidget.widget(page_index).setLayout(self.grid)
    #     logger.info(f"ui_gridview done")
    #     # self.start_video_capture()

    # def on_resize_event(self, widget_parent=None, event: QResizeEvent = None):
    #     self.parent_grid_width, self.parent_grid_height = widget_parent.width(), widget_parent.height()
    #     # Cancel any existing timer
    #     if self.resize_timer:
    #         self.resize_timer.cancel()

    #     # Start a new timer for delaying video capture
    #     # self.resize_timer = threading.Timer(0.2, self.start_video_capture)  # Delay of 0.5 seconds
    #     # self.resize_timer.start()

    # def item_resize_event(self, stack_item, event: QResizeEvent = None):
    #     # logger.info("item_resize_event")
    #     new_size = event.size()
    #     width = new_size.width()
    #     height = new_size.height()
    #     if stack_item is not None:
    #         stack_item.update_resize(width, height)
    #         if stack_item.currentWidget() is not None:
    #             stack_item.currentWidget().update_resize(width, height)
    #             # print(f'stack_item.index: {stack_item.index} - width: {width} - height: {height}')
    #             self.tab_model.data.listGridData[stack_item.index].virtual_width = width
    #             self.tab_model.data.listGridData[stack_item.index].virtual_height = height
    #     pass

    # def add_grid_item(self,stack_item:StackFrame = None,row = None,col = None,width = None, height = None, index = None):
    #     if index is None:
    #         index = self.get_index_of_item_from_row_col(row, col)
    #     stack_item.index = index
    #     grid_item:GridItem = self.tab_model.data.listGridData.get(index,None)
    #     stack_item.update_resize(width, height)
    #     if grid_item is None:
    #         # case tạo mới một tab nên không có thong tin grid_item nào
    #         widget = stack_item.add_widget()
    #         widget.stack_item = stack_item
    #         grid_item = GridItem(index = index,type=ItemType.Label, row = row, col = col,width = width,height = height,model= 'Label',widget = widget)
    #         self.tab_model.add_grid_item(grid_item)
    #         stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
    #     else:
    #         grid_item.index = index
    #         grid_item.row = row
    #         grid_item.col = col
    #         grid_item.virtual_width = width
    #         grid_item.virtual_height = height
    #         widget = grid_item.virtual_widget
    #         # logger.debug(f'ahihi = add_grid_item')
    #         if widget is not None:
    #             if grid_item.type == CommonEnum.ItemType.CAMERA:
    #                 if isinstance(widget, CameraWidget):
    #                     widget.stack_item = stack_item
    #                     # logger.debug(f'ahihi = add_grid_item')
    #                     stack_item.add_widget(widget=widget)
    #                     stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
    #                 else:
    #                     # logger.debug(f'gggg')
    #                     model:CameraModel = grid_item.model
    #                     self.add_camera_to_grid(camera_id=model.data.id,width=width,height=height,index_calculated=index,stack_item=stack_item,camera_model=model)
    #             elif grid_item.type == ItemType.Map:
    #                 if isinstance(widget, Map2DWidgetItem):
    #                     widget.stack_item = stack_item
    #                     widget.update_resize(width,height)
    #                     stack_item.add_widget(widget=widget)
    #                 else:
    #                     model:MapItem = grid_item.model
    #                     self.add_map_to_grid(map_item_model=model,stack_item=stack_item, item=grid_item, index_calculated = index)
    #             elif grid_item.type == CommonEnum.ItemType.EVENT:
    #                 if isinstance(widget, EventWidget):
    #                     widget.stack_item = stack_item
    #                     widget.update_resize(width,height)
    #                     stack_item.add_widget(widget=widget)
    #                 else:
    #                     model:EventAI = grid_item.model
    #                     self.add_event_to_grid(map_item_model=model,stack_item=stack_item, item=grid_item, index_calculated = index)
    #             else:
    #                 if isinstance(widget, BackgroundWidget):
    #                     widget.stack_item = stack_item
    #                     stack_item.add_widget(widget=widget)
    #                 else:
    #                     widget = stack_item.add_widget()
    #                     widget.stack_item = stack_item
    #                     grid_item.virtual_widget = widget
    #             # widget.stack_item = stack_item

    #         else:
    #             if grid_item.type == CommonEnum.ItemType.CAMERA:
    #                 model:CameraModel = grid_item.model
    #                 self.add_camera_to_grid(camera_id=model.data.id,width=width,height=height,index_calculated=index,stack_item=stack_item,camera_model=model)
    #             elif grid_item.type == ItemType.Map:
    #                 model:MapItem = grid_item.model
    #                 self.add_map_to_grid(map_item_model=model,stack_item=stack_item, item=grid_item, index_calculated = index)
    #             elif grid_item.type == CommonEnum.ItemType.EVENT:
    #                 model:EventAI = grid_item.model
    #                 self.add_event_to_grid(event_model=model,stack_item=stack_item, item=grid_item, index_calculated = index)
    #             else:
    #                 widget = stack_item.add_widget()
    #                 widget.stack_item = stack_item
    #                 grid_item.virtual_widget = widget
    #                 stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)

    # def add_camera_to_grid(self, camera_id = None, width = None, height = None, index_calculated = None, stack_item = None, camera_model = None,grid_number=1, is_fullscreen=False):
    #     camera_widget = CameraWidget(
    #             stream_link='', camera_name=camera_model.data.name, camera_id=camera_id,
    #             width=width, height=height,
    #             root_width=width, root_height=height, camera_model=camera_model,stream_type=CommonEnum.StreamType.SUB_STREAM,stack_item= stack_item,
    #             is_virtual_window=True,tab_model=self.tab_model
    #         )
    #     self.tab_model.data.listGridData[index_calculated].virtual_widget = camera_widget
    #     if is_fullscreen:
    #         # logger.debug(f'add_camera_to_grid3')
    #         stack_item_fullscreen = self.root_stackedwidget.currentWidget()
    #         camera_widget.update_resize(self.parent_grid_width, self.parent_grid_height)
    #         camera_widget.is_fullscreen = is_fullscreen
    #         stack_item_fullscreen.resizeEvent = lambda event: self.item_resize_event(stack_item_fullscreen, event)
    #         stack_item_fullscreen.load_widget(camera_widget)
    #     else:
    #         camera_widget.update_resize(width=width, height=height)
    #         current_index = stack_item.currentIndex()
    #         stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
    #         stack_item.load_widget(camera_widget)

    # def start_video_capture(self):
    #     if len(self.tab_model.data.listGridData) < 1:
    #         return
    #     for key,item in self.tab_model.data.listGridData.items():
    #         if isinstance(item.virtual_widget, CameraWidget):
    #             video_capture = item.virtual_widget.video_capture
    #             if not video_capture.isRunning():
    #                 # logger.debug(f'gggggg = {item.widget.camera_name}')
    #                 video_capture.start_thread()

    # def add_map_to_grid(self,map_item_model = None,stack_item: StackFrame = None,item = None, index_calculated = None,is_fullscreen = False):
    #     map_widget = Map2DWidgetItem(parent=stack_item, map_item_model = map_item_model, width=item.width, height=item.height, row=item.row, col=item.col, callback_fullscreen=None, stack_item=stack_item,tab_model=self.tab_model,is_virtual_window = True)
    #     map_widget.drop_when_not_editable_signal.connect(self.on_drop_object_to_map_not_editable)
    #     # map_widget.close_widget_signal.connect(self.btn_close_widget_clicked)
    #     self.tab_model.data.listGridData[index_calculated].virtual_widget = map_widget
    #     if is_fullscreen:
    #         # logger.debug(f'add_camera_to_grid3')
    #         stack_item_fullscreen = self.root_stackedwidget.currentWidget()
    #         map_widget.update_resize(self.parent_grid_width, self.parent_grid_height)
    #         stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
    #         map_widget.is_fullscreen = is_fullscreen
    #         stack_item_fullscreen.load_widget(map_widget)
    #     else:
    #         map_widget.update_resize(stack_item.frame_width, stack_item.frame_height)
    #         stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
    #         stack_item.load_widget(map_widget)

    # def on_drop_object_to_map_not_editable(self, value):
    #     event, row, col = value
    #     mime_data = event.mimeData()
    #     # send to drop event of stack item
    #     stack_item: StackFrame = self.grid.itemAtPosition(row, col).widget()
    #     stack_item.dropEvent(event)
    #     pass

    # def add_event_to_grid(self,event_model = None,stack_item: StackFrame = None,item = None, index_calculated = None, is_fullscreen=False):
    #     event_widget = EventWidget(parent=stack_item, event_model = event_model, width=item.width, height=item.height, row=item.row, col=item.col, stack_item=stack_item,tab_model=self.tab_model,is_virtual_window = True)

    #     # map_widget.close_widget_signal.connect(self.btn_close_widget_clicked)
    #     self.tab_model.data.listGridData[index_calculated].virtual_widget = event_widget
    #     if is_fullscreen:
    #         # logger.debug(f'add_camera_to_grid3')
    #         stack_item_fullscreen = self.root_stackedwidget.currentWidget()
    #         event_widget.update_resize(self.parent_grid_width, self.parent_grid_height)
    #         stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
    #         # event_widget.is_fullscreen = is_fullscreen
    #         stack_item_fullscreen.load_widget(event_widget)
    #     else:
    #         event_widget.update_resize(stack_item.frame_width, stack_item.frame_height)
    #         stack_item.resizeEvent = lambda event: self.item_resize_event(stack_item, event)
    #         stack_item.load_widget(event_widget)

    # def add_grid_item_signal(self, index):
    #     # print("VirtualCameraGridWidget: on_drop_event ")
    #     # data,stack_item = value
    #     # item = GridItem.from_dict(data)
    #     grid_item = self.tab_model.data.listGridData.get(index,None)
    #     if grid_item is not None:
    #         if grid_item.type == CommonEnum.ItemType.CAMERA:
    #             is_fullscreen = False
    #             virtual_widget = grid_item.virtual_widget  
    #             stack_item = virtual_widget.stack_item
    #             # stop luồng camera nếu widget này là CameraWidget
    #             if isinstance(virtual_widget,CameraWidget):
    #                 self.stop_live_handle(virtual_widget)
    #                 is_fullscreen = virtual_widget.is_fullscreen

    #             # sorted_dict = {k: v for k, v in sorted(self.tab_model.data.listGridData.items())}
    #             # self.tab_model.data.listGridData = sorted_dict
    #             final_url_stream = grid_item.model.data.urlMainstream
    #             final_camera_name = grid_item.model.data.name
    #             final_id = grid_item.model.data.id
    #             # Bắt đầu add CameraWidget
    #             self.add_camera_to_grid(camera_id=final_id,width=grid_item.virtual_width,height=grid_item.virtual_height,index_calculated=index,stack_item=stack_item,camera_model=grid_item.model,is_fullscreen=grid_item.is_virtual_fullscreen)
                
    #             # self.start_video_capture()
    #             # nếu không phải tab virtual hoăc saved view thì lưu vào QSetting
    #             # if self.tab_type != TabType.Invalid:
    #             #     self.tab_model.to_qsetting()
    #             # else:
    #             #     self.custom_tab_widget.tab_widget.tab_bar.setTabText(self.tab_index,f'{self.tab_name}*')
    #         elif grid_item.type == ItemType.Map:
    #             is_fullscreen = False
    #             virtual_widget = grid_item.virtual_widget  
    #             stack_item = virtual_widget.stack_item
    #             # stop luồng camera nếu widget này là CameraWidget
    #             if isinstance(virtual_widget,CameraWidget):
    #                 self.stop_live_handle(virtual_widget)
    #                 is_fullscreen = virtual_widget.is_fullscreen
    #             # Update list_grid_item
            
    #             # Bắt đầu add CameraWidget
    #             self.add_map_to_grid(map_item_model=grid_item.model,stack_item=stack_item, item=grid_item, index_calculated = index,is_fullscreen=grid_item.is_virtual_fullscreen)
       
    #         elif grid_item.type == CommonEnum.ItemType.EVENT:
    #             is_fullscreen = False
    #             virtual_widget = grid_item.virtual_widget  
    #             stack_item = virtual_widget.stack_item
    #             # stop luồng camera nếu widget này là CameraWidget
    #             if isinstance(virtual_widget,CameraWidget):
    #                 self.stop_live_handle(virtual_widget)
    #                 is_fullscreen = virtual_widget.is_fullscreen
    #             elif isinstance(virtual_widget,Map2DWidgetItem):
    #                 is_fullscreen = virtual_widget.is_fullscreen
    #             elif isinstance(virtual_widget,EventWidget):
    #                 pass
    #             # Update list_grid_item
            
    #             # Bắt đầu add CameraWidget
    #             self.add_event_to_grid(event_model=grid_item.model,stack_item=stack_item, item=grid_item, index_calculated = index,is_fullscreen = grid_item.is_virtual_fullscreen)

    #     # print(f'on_drop_event = {grid_item_selected.data}')
    # def swap_grid_item_signal(self, value):
    #     new_index,old_index = value
    #     new_stack:StackFrame = self.grid.itemAt(new_index).widget()
    #     old_stack:StackFrame = self.grid.itemAt(old_index).widget()
    #     new_grid_item:GridItem = self.tab_model.data.listGridData.get(new_index,None)
    #     if new_grid_item is not None:
    #         new_temp_widget = new_stack.currentWidget()
    #     old_grid_item:GridItem = self.tab_model.data.listGridData.get(old_index,None)

    #     if old_grid_item is not None:
    #         old_temp_widget = old_stack.currentWidget()

    #     new_stack.removeWidget(new_stack.currentWidget())
    #     old_stack.removeWidget(old_stack.currentWidget())

    #     new_stack.load_widget(old_temp_widget)
    #     old_temp_widget.stack_item = new_stack
    #     new_stack.currentWidget().update_resize(new_stack.size().width(),new_stack.size().height())
    #     new_stack.update_resize(new_stack.size().width(),new_stack.size().height())

    #     old_stack.load_widget(new_temp_widget)
    #     new_temp_widget.stack_item = old_stack
    #     old_stack.currentWidget().update_resize(old_stack.size().width(),old_stack.size().height())
    #     old_stack.update_resize(old_stack.size().width(),old_stack.size().height())

    #     # # nếu không phải tab virtual hoăc saved view thì lưu vào QSetting
    #     # if self.tab_type != TabType.Invalid:
    #     #     self.tab_model.to_qsetting()
    #     # else:
    #     #     self.custom_tab_widget.tab_widget.tab_bar.setTabText(self.tab_index,f'{self.tab_name}*')
        
    # def stop_live_handle(self, data):
    #     widget = data
    #     camera_id = widget.camera_id
    #     video_capture_controller.unregister_video_capture(widget)
    #     if widget.is_fullscreen:
    #         parent_widget = widget.parentWidget()
    #         print(f'parent_widget: {parent_widget}')
    #         stack_item = widget.stack_item
    #         if parent_widget is not None:
    #             # kiem tra xem camera nay co dang duoc chon grid_item_selected khong
    #             if grid_item_selected.data['tab_index'] is not None and grid_item_selected.is_tab_index(main_controller.current_tab) and camera_id == grid_item_selected.data['camera_id']:
    #                 # grid_item_selected.clear()
    #                 widget.grid_item_unclicked()
    #             # widget.deleteLater()
    #             widget = None
    #     else:
    #         # get parrent_widget chua camera_widget nay de remove khoi QStackWidget
    #         stack_item = widget.stack_item
    #         # kiem tra xem camera nay co dang duoc chon grid_item_selected khong
    #         if grid_item_selected.data['tab_index'] is not None and grid_item_selected.is_tab_index(main_controller.current_tab) and camera_id == grid_item_selected.data['camera_id']:
    #             # grid_item_selected.clear()
    #             widget.grid_item_unclicked()
    #         # widget.deleteLater()
    #     # main_controller.gc_collect(self)

    # def fullscreen_grid_item_signal(self,index):
        
    #     grid_item = self.tab_model.data.listGridData.get(index,None)
    #     if grid_item is not None:
    #         if self.tab_model.data.currentGrid['total_grid_count'] == 1:
    #             return None
    #         object_widget: StreamObjectBaseWidget = grid_item.virtual_widget
            
    #         # index_calculated = None
    #         # for idx, grid_item in self.tab_model.data.listGridData.items():
    #         #     grid_item: GridItem
    #         #     if object_widget == grid_item.widget:
    #         #         index_calculated = idx
            
    #         stack_item: StackFrame = object_widget.stack_item
    #         if grid_item.is_virtual_fullscreen:
    #             # print(f'FULL to NORMAL {index_calculated}')
    #             # exit fullscreen
    #             grid_item.is_virtual_fullscreen = False
    #             object_widget.btn_full_screen.set_icon(main_controller.get_theme_attribute("Image", "expand_camera"))
    #             # camera_widget.update_border()
    #             if isinstance(object_widget, CameraWidget):
    #                 object_widget.update_resize(self.original_width, self.original_height)
    #                 if self.tab_model.data.currentGrid['row'] >= 3:
    #                     current_stream_type = object_widget.video_capture.stream_type
    #                     if current_stream_type in [CommonEnum.StreamType.MAIN_STREAM, CommonEnum.StreamType.SUB_STREAM]:
    #                         object_widget.switch_video_capture(CommonEnum.StreamType.SUB_STREAM)
    #             if isinstance(object_widget, Map2DWidgetItem):
    #                 object_widget.update_resize(self.original_width, self.original_height)
    #             if isinstance(object_widget, MapGridWidgetItem):
    #                 object_widget.update_resize(self.original_width, self.original_height)
    #             if isinstance(object_widget, EventWidget):
    #                 object_widget.update_resize(self.original_width, self.original_height)
    #             # create new stack item and add old stack_item to new
    #             # stack_item_new = stack_item.clone()
    #             # widget = self.grid.itemAtPosition(stack_item.position.x(),stack_item.position.y()).widget()
    #             # self.grid.replaceWidget(widget,stack_item_new)

    #             stack_item_new = self.grid.itemAt(stack_item.index).widget()
    #             stack_item_new.load_widget(object_widget)
    #             object_widget.stack_item = stack_item_new
    #             self.root_stackedwidget.removeWidget(stack_item)
    #             self.root_stackedwidget.setCurrentIndex(0)
                
    #             # update size stack item
    #             stack_item_new.update_resize(self.original_width, self.original_height)
                
    #             self.tab_model.data.listGridData[index].virtual_widget = object_widget

    #             if isinstance(object_widget, CameraWidget):
    #                 pass
    #         else:
    #             grid_item.is_virtual_fullscreen = True
    #             object_widget.btn_full_screen.set_icon(main_controller.get_theme_attribute("Image", "shrink_camera"))
    #             # camera_widget.update_border()
    #             self.original_width = object_widget.root_width
    #             self.original_height = object_widget.root_height
                
    #             if isinstance(object_widget, CameraWidget):
    #                 object_widget.update_resize(self.parent_grid_width, self.parent_grid_height)
    #                 current_stream_type = object_widget.video_capture.stream_type
    #                 if current_stream_type in [CommonEnum.StreamType.MAIN_STREAM, CommonEnum.StreamType.SUB_STREAM]:
    #                     object_widget.switch_video_capture(CommonEnum.StreamType.MAIN_STREAM)
    #             if isinstance(object_widget, Map2DWidgetItem):
    #                 object_widget.update_resize(self.parent_grid_width, self.parent_grid_height)
    #             if isinstance(object_widget, EventWidget):
    #                 object_widget.update_resize(self.parent_grid_width, self.parent_grid_height)
    #             stack_item_new = stack_item.clone()
    #             stack_item_new.load_widget(object_widget)
    #             object_widget.stack_item = stack_item_new

    #             self.root_stackedwidget.addWidget(stack_item_new)
    #             self.root_stackedwidget.setCurrentWidget(stack_item_new)
    #             # update size stack item
    #             stack_item_new.update_resize(self.parent_grid_width, self.parent_grid_height)
    #             self.tab_model.data.listGridData[index].virtual_widget = object_widget

    #             if isinstance(object_widget, CameraWidget):
    #                 logger.info(f"Virtula = {object_widget.video_capture}")
    #                 # Switch camera stream:
    #                 # if object_widget.video_capture.stream_type != StreamCameraType.ai_stream:
    #                 #     if object_widget.video_capture.stream_type == CommonEnum.StreamType.MAIN_STREAM:
    #                 #         pass
    #                 #     else:
    #                 #         object_widget.switch_video_capture(CommonEnum.StreamType.MAIN_STREAM)
    #                 #         self.model.switch_video_capture_signal.emit(
    #                 #             (object_widget.camera_model.data.id, CommonEnum.StreamType.MAIN_STREAM, index))
    #         # logger.debug(f'fullscreen_grid_item = {self.tab_model.data.listGridData}')
    #         return object_widget
        
    # def on_drop_group(self, list_camera_in_group: List[Camera]):
    #     pass

    # def process_shortcut_id(self,id = None, tree_type = None):
    #     widget = grid_item_selected.data['widget']
    #     for key, grid_item in self.tab_model.data.listGridData.items():
    #         if grid_item.virtual_widget == widget:
    #             stack_item = widget.stack_item
    #             # logger.info(f"process_shortcut_id1 = {widget}")
    #             width = stack_item.frame_width
    #             height = stack_item.frame_height
    #             # stack_view = grid_item.widget.stack_item
    #             camera_model = camera_model_manager.get_camera_model(id = id)

    #             grid_item = self.tab_model.data.listGridData.get(key,None)
    #             if grid_item.model == camera_model:
    #                 # case kéo một event trùng nhau
    #                 return
    #             if  grid_item is not None:
    #                 grid_item.type = CommonEnum.ItemType.CAMERA
    #                 # grid_item.row = stack_item.position.x()
    #                 # grid_item.col = stack_item.position.y()
    #                 # grid_item.width = stack_item.frame_width
    #                 # grid_item.height = stack_item.frame_height
    #                 self.tab_model.set_model(grid_item = grid_item,model = camera_model)
    #             self.tab_model.add_grid_item_signal.emit(key)
    #             # self.tab_model.data.listGridData[key].virtual_widget.stack_item.grid_item_clicked(main_controller.current_tab)
    #             break


    # def span_and_create_custom_divisions(self, size=None, excluded_positions=None, rows=None, cols=None):
    #     if excluded_positions is not None:
    #         list_grid_item = {}
    #         new_data = []
    #         for item in excluded_positions:
    #             list_to_tuple = set()
    #             if isinstance(item, list):
    #                 for list_item in item:
    #                     list_to_tuple.add(tuple(list_item))
    #                 new_data.append(set(list_to_tuple))
    #             else:
    #                 new_data.append(set(item))
    #         all_excluded_positions = set.union(*new_data)
    #         self.frame_width = int((self.grid_width - (cols-1)) / cols)
    #         self.frame_height = int((self.grid_height - rows) / rows)


    #         for list_tuple in new_data:
    #             # Determine min_row, min_col, and span for the current largest_item
    #             min_row = min(row for row, _ in list_tuple)
    #             min_col = min(col for _, col in list_tuple)
    #             max_row = max(row for row, _ in list_tuple)
    #             max_col = max(col for _, col in list_tuple)

    #             row_span = max_row - min_row + 1
    #             col_span = max_col - min_col + 1
 
    #             width_large = col_span*self.frame_width + (col_span-1)
    #             height_large = row_span*self.frame_height + row_span
    #             # width_large = col_span * self.frame_width + (row_span - 1)
    #             # height_large = row_span * self.frame_height + (col_span - 1)

    #             root_camera_large = StackFrame(parent=self, frame_width=width_large,
    #                                            frame_height=height_large, position=QPoint(min_row, min_col))
    #             root_camera_large.resizeEvent = lambda event: self.item_resize_event(root_camera_large, event)
    #             root_camera_large.setSizeIncrement(width_large, height_large)
    #             root_camera_large.drop_group_signal.connect(self.on_drop_group)
    #             self.grid.addWidget(root_camera_large, min_row, min_col, row_span, col_span)
                
    #             grid_item_data = {
    #                 'stack_item': root_camera_large,
    #                 'row': min_row,
    #                 'col': min_col,
    #                 'width': width_large,
    #                 'height': height_large
    #             }
    #             list_grid_item[(min_row, min_col)] = grid_item_data
    #             # self.add_grid_item(stack_item = root_camera_large, row=min_row,col=min_col,width=width_large,height=height_large)
                

    #         # Create items surrounding the largest_item
    #         for row in range(rows):
    #             for col in range(cols):
    #                 if (row, col) in all_excluded_positions:
    #                     continue  # Skip the specified positions
    #                 # camera_frame_small = QLabel()
    #                 # camera_frame_small.mousePressEvent = self.frame_press_event
    #                 # camera_frame_small.setStyleSheet(Style.StyleSheet.camera_frame)

    #                 # camera_frame_small.setFixedSize(cell_width, cell_height)
    #                 root_camera_widget_small = StackFrame(parent=self, frame_width=self.frame_width,
    #                                                       frame_height=self.frame_height, position=QPoint(row, col))
    #                 root_camera_widget_small.resizeEvent = lambda event: self.item_resize_event(root_camera_widget_small, event)
    #                 root_camera_widget_small.drop_group_signal.connect(self.on_drop_group)
    #                 self.grid.addWidget(root_camera_widget_small, row, col)
    #                 grid_item_data = {
    #                     'stack_item': root_camera_widget_small,
    #                     'row': row,
    #                     'col': col,
    #                     'width': self.frame_width,
    #                     'height': self.frame_height
    #                 }
    #                 list_grid_item[(row, col)] = grid_item_data
    #                 # self.add_grid_item(stack_item = root_camera_widget_small, row=row,col=col,width=self.frame_width,height=self.frame_height)


    #         # Create a list to store widgets and their positions
    #         widget_positions = []
    #         for index in range(self.grid.count()):
    #             item = self.grid.itemAt(index)
    #             re_row, re_col, re_row_span, re_col_span = self.grid.getItemPosition(index)
    #             widget_positions.append((item.widget(), re_row, re_col, re_row_span, re_col_span))

    #         # Sort the list based on positions
    #         widget_positions.sort(key=lambda x: (x[1], x[2]))
    #         # print(f"HanhLT: widget_positions = {widget_positions}")
    #         # for indx, item in enumerate(widget_positions):    
    #         #     print(f"HanhLT: idx {indx}  item = {item}")
    #         # Chỗ này Clear the existing widgets đi
    #         # Nếu ko clear thì sẽ bị stack layout lên
    #         for i in reversed(range(self.grid.count())):
    #             widget = self.grid.itemAt(i).widget()
    #             self.grid.removeWidget(widget)
    #             widget.setParent(None)

    #         # Rearrange widgets in the layout
    #         for new_index, (widget, re_row, re_col, re_row_span, re_col_span) in enumerate(widget_positions):
    #             self.grid.addWidget(widget, re_row, re_col, re_row_span, re_col_span)
    #             # Optionally, set row and column stretch to manage spacing
    #             self.grid.setRowStretch(re_row, 1)
    #             self.grid.setColumnStretch(re_col, 1)

    #         # Update the layout
    #         self.grid.update()

    #         for key, value in list_grid_item.items():
    #             row = value['row']
    #             col = value['col']
    #             width = value['width']
    #             height = value['height']
    #             stack_item = value['stack_item']
    #             self.add_grid_item(stack_item = stack_item,row=row,col=col,width=width,height=height)


    # def closeEvent(self, event):
    #     # close stream self.list_camera_stream_widget
    #     # for key, camera_stream in self.list_camera_stream_widget.items():
    #     #     camera_stream: CameraStream
    #     #     camera_stream.close()
    #     for name, grid_item in self.tab_model.data.listGridData.items():
    #         grid_item.is_virtual_fullscreen = False
    #         if grid_item.virtual_widget is not None and isinstance(grid_item.virtual_widget,CameraWidget):
    #             video_capture_controller.unregister_video_capture(grid_item.virtual_widget)
    #             grid_item.virtual_widget.close()
    #             grid_item.virtual_widget = None
    #     if self.tab_model is not None:
    #         self.tab_model.unregister_signal(self)
    #         main_tree_view = main_controller.list_parent['MainTreeView']
    #         item  = main_tree_view.get_item(name=self.tab_model.data.name,tree_type = TreeType.Virtual_Window_Item,server_ip = self.tab_model.data.server_ip)
    #         if item is not None:
    #             item.setIcon(QIcon(main_controller.get_theme_attribute('Image', 'close_all_virtual')))
    #         # self.item.setIcon(QIcon(Style.PrimaryImage.close_all_virtual))
    #         if self.tab_model.data.id in main_controller.list_parent:
    #             del main_controller.list_parent[self.tab_model.data.id]
    #     new_custom_tab_widget = main_controller.list_parent['CustomTabWidget']
    #     # trường hợp người dùng đóng cưỡng bức cửa sổ Virtual thì cần tìm tab Virtual để đóng nó lại
    #     if not self.tab_closed:
    #         for index in range(new_custom_tab_widget.tab_widget.count()):
    #             widget = new_custom_tab_widget.getWidgetByIndex(index)
    #             tab_name = new_custom_tab_widget.tab_widget.tab_bar.tabText(
    #                 index)
    #             if widget.tab_model.data.id == self.tab_model.data.id:
    #                 new_custom_tab_widget.tab_widget.removeTab(index)
    #                 break


    # def restyle_virtual_widget(self):
    #     self.main_widget.setStyleSheet(f'''
    #                 QWidget#main_widget {{
    #                     background-color: {main_controller.get_theme_attribute('Image', 'main_background')};
    #                 }}
    #             ''')

    #     if self.grid is not None:
    #         for index in range(self.grid.count()):
    #             item = self.grid.itemAt(index)
    #             if item is not None:
    #                 widget: StackFrame = item.widget()
    #                 if isinstance(widget.currentWidget(), CameraWidget):
    #                     widget.currentWidget().update_dynamic_stylesheet()
    #                 else:
    #                     if widget.background_widget is not None:
    #                         widget.background_widget.background_label.setStyleSheet(f'''
    #                             background-color: {main_controller.get_theme_attribute("Color", "camera_widget_background")};
    #                         ''')
    #     self.grid.update()


